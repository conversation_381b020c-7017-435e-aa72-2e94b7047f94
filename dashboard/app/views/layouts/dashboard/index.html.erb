<% content_for :content do %>
  <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
    <div style="max-width: 1280px;">
      <div style="display: flex;gap: 20px;">
        <div>
          <div style="height: 60px;margin: 16px 0;"></div>
          <div class="left1">
            <div style="text-align:center;margin-top: -20px;">
              <div>
                <% user_info = UserInfo.find_by(username: @user.name) %>
                <img class="avatar" src="/pun/sys/dashboard<%= user_info&.avatar_url || '/new/home/<USER>' %>">
              </div>
              <div>
                <%= @user.name %>
              </div>
              <div style="color: #007AFF">
                <a href="/pun/sys/dashboard/user/change_password">账号设置</a>
              </div>
              <div style="margin-top: 16px;width: 248px;text-align: left;">
                剩余机时
              </div>
              <div style="display: flex;gap: 32px;justify-content: space-around;margin-top:8px;">
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div><a href="/pun/sys/dashboard/machine_time?tab=cpu">3.4小时</a></div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div><a href="/pun/sys/dashboard/machine_time?tab=gpu">2.4小时</a></div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div><a href="/pun/sys/dashboard/machine_time?tab=ram">1.4小时</a></div>
                </div>
              </div>
              <div style="display: flex;justify-content: left;flex-direction: column;">
                <div style="margin-top: 24px;width: 248px;text-align: left;">
                  账户余额
                </div>
                <div style="display: flex;align-items: baseline;">
                  <div>¥</div>
                  <% user_info = UserInfo.find_by(username: @user.name) %>
                  <div style="font-size: 20px;font-weight: 600;"><%= user_info.balance %></div>
                </div>
              </div>


              <div style="display: flex;margin: 4px 0px">
                <div style="display: flex;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">账户记录</div>
                </div>
                <div style="display: flex;margin-left: 16px;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">扫码充值</div>
                </div>
              </div>
              <div style="width: 248px;text-align:start;">
                优先扣除剩余机时，任有一项硬配时长不足将按计费标准扣除余额。
              </div>
            </div>
          </div>
          <div class="left2">
            <div>
              <div style="margin-top: 24px;width: 248px;text-align: left;">
                累计耗时
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                12时05分
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                成功完成28次AI训练，机时平均消耗3 分16秒
              </div>
            </div>
            <%= render "layouts/dashboard/task_template" %>
          </div>
        </div>
        <div style="overflow-y: scroll;height: 100vh;">
          <div class="header" style="display: flex;justify-content: space-between;">
            <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
            <div style="display: flex;align-items:center;">
              <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
              <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
            </div>
          </div>
          <%= render "layouts/dashboard/right1" %>
          <!-- <div class="right2">
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
          </div> -->
          <%= render "layouts/dashboard/right3" %>
        </div>
      </div>
    </div>
  </div>

  <div class="dock">
    <div class="dock-item with-divider">
      <a href="/pun/sys/dashboard/activejobs/status?status=all"><%= image_tag "/new/home/<USER>" %></a>
      <span>全部</span>
    </div>
    <div class="dock-item">
      <a href="/pun/sys/dashboard/activejobs/status?status=queued"><%= image_tag "/new/home/<USER>" %></a>
      <span>队列中</span>
    </div>
    <div class="dock-item">
      <a href="/pun/sys/dashboard/activejobs/status?status=status-running"><%= image_tag "/new/home/<USER>" %></a>
      <span>进行中</span>
    </div>
    <div class="dock-item">
      <a href="/pun/sys/dashboard/activejobs/status?status=completed"><%= image_tag "/new/home/<USER>" %></a>
      <span>已完成</span>
    </div>
    <div class="dock-item">
      <a href="/pun/sys/dashboard/activejobs/status?status=failed"><%= image_tag "/new/home/<USER>" %></a>
      <span>已终止</span>
    </div>
  </div>
  <%#= image_tag '/new/home_bg.png' %>


<% end %>
<%= render 'layouts/dashboard/base' %>