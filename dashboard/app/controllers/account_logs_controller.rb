class AccountLogsController < ApplicationController
  before_action :set_layout_container_class

  def index
    @page = params[:page] || 1

    # 获取账户记录数据
    @account_logs = AccountLog.where(username: @user.name)
                             .order(created_at: :desc)
                             .page(@page)
                             .per(10)
  end

  private

  def set_layout_container_class
    @layout_container_class = 'container-md'
  end
end
