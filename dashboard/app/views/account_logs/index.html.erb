<% content_for :content do %>
  <style>
    .content-body {
      height: calc(100vh - 100px);
      background: white;
      border-radius: 16px;
      padding: 22px 30px;
    }
    .submit-button {
      font-size: 14px;
      padding: 12px;
      background: #007Aff;
    }
    .avatar-container {
      text-align: center;
    }
    .pagination {
      display: flex;
      padding-left: 0;
      list-style: none;
      border-radius: 0.25rem;
    }
    .page-item {
      margin: 0 2px;
    }
    .page-link {
      position: relative;
      display: block;
      padding: 0.5rem 0.75rem;
      margin-left: -1px;
      line-height: 1.25;
      color: #007bff;
      background-color: #fff;
      border: 1px solid #dee2e6;
      text-decoration: none;
      border-radius: 0.25rem;
    }
    .page-link:hover {
      z-index: 2;
      color: #0056b3;
      text-decoration: none;
      background-color: #e9ecef;
      border-color: #dee2e6;
    }
    .page-item.active .page-link {
      z-index: 3;
      color: #fff;
      background-color: #007bff;
      border-color: #007bff;
    }
    .page-item.disabled .page-link {
      color: #6c757d;
      pointer-events: none;
      cursor: auto;
      background-color: #fff;
      border-color: #dee2e6;
    }
  </style>
  <div class="header" style="display: flex;justify-content: space-between;">
    <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
    <div style="display: flex;align-items:center;">
      <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
      <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
    </div>
  </div>
  <div class="content-body">
    <a class="logout-text" href="/pun/sys/dashboard">
      <%= image_tag "/new/home/<USER>", style: "width: 30px;height: 30px;" %>
    </a>
    <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
      <div style="width: 1280px;display: flex;justify-content:center;" class="row">
        <div class="col-1211" style="width: 640px;">
          <div class="d-flex justify-content-center border-bottom mb-2" style="gap: 2rem;font-size: 18px;">
            <div class="pb-2" style="color: #007bff; border-bottom: 2.5px solid #007bff; font-weight: 500;">
              账户记录
            </div>
          </div>
        </div>
        <!-- 账户记录列表 -->
        <div class="col-1211" style="width: 640px;">
          <div class="card" style="border: none;">
            <div class="card-body p-0">
              <div class="table-responsive">
                <% if @account_logs.any? %>
                  <% @account_logs.each_with_index do |log, index| %>
                    <div style="border-bottom: 1px;margin-top: 22px;margin-bottom:33px;">
                      <div style="display: flex;justify-content:space-between;font-size: 14px;font-weight: 600;">
                        <div><%= log.title %></div>
                        <div style="color:<%= log.changed_balance >= 0 ? '#28a745' : '#FF4949' %>;"><%= log.changed_balance >= 0 ? '+' : '' %><%= log.changed_balance %></div>
                      </div>
                      <div style="font-size: 12px;"><%= log.created_at.strftime('%Y.%m.%d %H:%M:%S') %></div>
                      <div style="font-size: 12px;color:#666666;display: flex;justify-content:space-between;">
                        <div><%= log.description %></div>
                        <div>余额：<%= log.after_balance %></div>
                      </div>
                    </div>
                  <% end %>

                  <!-- 手动分页 -->
                  <% if @total_pages > 1 %>
                    <div class="d-flex justify-content-center mt-4">
                      <nav aria-label="分页导航">
                        <ul class="pagination">
                          <!-- 上一页 -->
                          <% if @has_prev %>
                            <li class="page-item">
                              <%= link_to account_logs_path(page: @prev_page), class: "page-link" do %>
                                <span aria-hidden="true">&laquo;</span>
                                <span class="sr-only">上一页</span>
                              <% end %>
                            </li>
                          <% else %>
                            <li class="page-item disabled">
                              <span class="page-link">
                                <span aria-hidden="true">&laquo;</span>
                                <span class="sr-only">上一页</span>
                              </span>
                            </li>
                          <% end %>

                          <!-- 页码 -->
                          <% start_page = [@page - 2, 1].max %>
                          <% end_page = [start_page + 4, @total_pages].min %>
                          <% start_page = [end_page - 4, 1].max if end_page - start_page < 4 %>

                          <% if start_page > 1 %>
                            <li class="page-item">
                              <%= link_to "1", account_logs_path(page: 1), class: "page-link" %>
                            </li>
                            <% if start_page > 2 %>
                              <li class="page-item disabled">
                                <span class="page-link">...</span>
                              </li>
                            <% end %>
                          <% end %>

                          <% (start_page..end_page).each do |page_num| %>
                            <% if page_num == @page %>
                              <li class="page-item active">
                                <span class="page-link"><%= page_num %></span>
                              </li>
                            <% else %>
                              <li class="page-item">
                                <%= link_to page_num, account_logs_path(page: page_num), class: "page-link" %>
                              </li>
                            <% end %>
                          <% end %>

                          <% if end_page < @total_pages %>
                            <% if end_page < @total_pages - 1 %>
                              <li class="page-item disabled">
                                <span class="page-link">...</span>
                              </li>
                            <% end %>
                            <li class="page-item">
                              <%= link_to @total_pages, account_logs_path(page: @total_pages), class: "page-link" %>
                            </li>
                          <% end %>

                          <!-- 下一页 -->
                          <% if @has_next %>
                            <li class="page-item">
                              <%= link_to account_logs_path(page: @next_page), class: "page-link" do %>
                                <span aria-hidden="true">&raquo;</span>
                                <span class="sr-only">下一页</span>
                              <% end %>
                            </li>
                          <% else %>
                            <li class="page-item disabled">
                              <span class="page-link">
                                <span aria-hidden="true">&raquo;</span>
                                <span class="sr-only">下一页</span>
                              </span>
                            </li>
                          <% end %>
                        </ul>
                      </nav>
                    </div>

                    <!-- 分页信息 -->
                    <div class="text-center mt-2" style="font-size: 12px; color: #666;">
                      显示第 <%= (@page - 1) * @per_page + 1 %> - <%= [@page * @per_page, @total_count].min %> 条，共 <%= @total_count %> 条记录
                    </div>
                  <% end %>
                <% else %>
                  <div class="text-muted text-center" style="padding: 60px 0;">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p class="mb-0">暂无记录</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%= render 'layouts/dashboard/base' %>
