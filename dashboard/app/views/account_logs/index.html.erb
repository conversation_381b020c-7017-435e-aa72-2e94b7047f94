<% content_for :content do %>
  <style>
    .content-body {
      height: calc(100vh - 100px);
      background: white;
      border-radius: 16px;
      padding: 22px 30px;
    }
    .submit-button {
      font-size: 14px;
      padding: 12px;
      background: #007Aff;
    }
    .avatar-container {
      text-align: center;
    }
  </style>
  <div class="header" style="display: flex;justify-content: space-between;">
    <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
    <div style="display: flex;align-items:center;">
      <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
      <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
    </div>
  </div>
  <div class="content-body">
    <a class="logout-text" href="/pun/sys/dashboard">
      <%= image_tag "/new/home/<USER>", style: "width: 30px;height: 30px;" %>
    </a>
    <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
      <div style="width: 1280px;display: flex;justify-content:center;" class="row">
        <div class="col-1211" style="width: 640px;">
          <div class="d-flex justify-content-center border-bottom mb-2" style="gap: 2rem;font-size: 18px;">
            <div class="pb-2" style="color: #007bff; border-bottom: 2.5px solid #007bff; font-weight: 500;">
              账户记录
            </div>
          </div>
        </div>
        <!-- 账户记录列表 -->
        <div class="col-1211" style="width: 640px;">
          <div class="card" style="border: none;">
            <div class="card-body p-0">
              <div class="table-responsive">
                <% if @account_logs.any? %>
                  <% @account_logs.each_with_index do |log, index| %>
                    <div style="border-bottom: 1px;margin-top: 22px;margin-bottom:33px;">
                      <div style="display: flex;justify-content:space-between;font-size: 14px;font-weight: 600;">
                        <div><%= log.title %></div>
                        <div style="color:<%= log.changed_balance >= 0 ? '#28a745' : '#FF4949' %>;"><%= log.changed_balance >= 0 ? '+' : '' %><%= log.changed_balance %></div>
                      </div>
                      <div style="font-size: 12px;"><%= log.created_at.strftime('%Y.%m.%d %H:%M:%S') %></div>
                      <div style="font-size: 12px;color:#666666;display: flex;justify-content:space-between;">
                        <div><%= log.description %></div>
                        <div>余额：<%= log.after_balance %></div>
                      </div>
                    </div>
                  <% end %>
                  
                  <!-- 分页 -->
                  <div class="d-flex justify-content-center mt-4">
                    <%= paginate @account_logs, theme: 'twitter-bootstrap-4' %>
                  </div>
                <% else %>
                  <div class="text-muted text-center" style="padding: 60px 0;">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p class="mb-0">暂无记录</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%= render 'layouts/dashboard/base' %>
